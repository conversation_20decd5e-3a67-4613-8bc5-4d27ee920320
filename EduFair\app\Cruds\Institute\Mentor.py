from typing import List, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta
import bcrypt
import json

# Import Models
from Models.users import (
    User, UserTypeEnum, MentorProfile, InstituteProfile,
    MentorInstituteAssociation, MentorInstituteInvite as MentorInstituteInviteModel, Subject
)
from Models.Competitions import CompetitionMentorAssignment

# Import Schemas
from Schemas.Mentors.Mentor import (
    MentorProfileUpdate,
    MentorUserOut, MentorDetailedOut, MentorProfileOut, MentorListOut, MentorListResponse
)
from Schemas.Mentors.MentorInstitutes import (
    MentorInstituteInvite as MentorInstituteInviteSchema, MentorInstituteInviteOut, InvitationListResponse,
    CollaborationDetails, CollaborationCreate, CollaborationUpdate, InvitationSenderDetails
)
from Schemas.Institute.Institute import InstituteListOut

# Import Utilities
from utils.image_utils import get_profile_image_data


def _populate_sender_details(db: Session, invite: MentorInstituteInviteModel, data: Optional[MentorProfile | InstituteProfile]) -> Optional[InvitationSenderDetails]:
    """Helper function to populate sender details for invitations"""

    if not invite or not invite.received_by:
        return None

    # Determine who is the sender based on received_by field
    if invite.received_by == "mentor":
        # Institute sent the invite to mentor - fetch institute user and profile
        sender_user = db.query(User).filter(
            User.id == invite.institute_id,
            User.user_type == UserTypeEnum.institute
        ).first()

        if not sender_user or not sender_user.country:
            return None

        # Fetch institute profile separately
        institute_profile = db.query(InstituteProfile).filter(
            InstituteProfile.user_id == invite.institute_id
        ).first()

        # Get profile image data
        profile_image = None
        if sender_user.profile_picture:
            try:
                profile_image = get_profile_image_data(sender_user.profile_picture)
            except Exception:
                profile_image = None

        # Prepare institute-specific details
        institute_name = "Unknown Institute"
        institute_description = None
        institute_website = None
        institute_city = None
        institute_country = sender_user.country
        institute_is_verified = False
        institute_logo = None

        # Populate institute profile details
        if institute_profile:
            institute_name = institute_profile.institute_name or "Unknown Institute"
            institute_description = institute_profile.description
            institute_website = institute_profile.website
            institute_city = institute_profile.city
            institute_is_verified = institute_profile.is_verified or False

            # Get institute logo image data
            if institute_profile.logo_url:
                try:
                    institute_logo = get_profile_image_data(institute_profile.logo_url)
                except Exception:
                    institute_logo = None

        try:
            return InvitationSenderDetails(
                id=sender_user.id,
                username=sender_user.username or "",
                email=sender_user.email or "",
                profile_picture=sender_user.profile_picture,
                profile_image=profile_image,
                # Institute-specific fields
                institute_name=institute_name,
                institute_description=institute_description,
                institute_website=institute_website,
                institute_city=institute_city,
                institute_country=institute_country,
                institute_is_verified=institute_is_verified,
                institute_logo=institute_logo,
                # Mentor fields set to None for institute sender
                mentor_bio=None,
                mentor_experience_years=None,
                mentor_hourly_rate=None,
                mentor_languages=None
            )
        except Exception as e:
            print(f"Error creating InvitationSenderDetails for institute: {e}")
            return None

    elif invite.received_by == "institute":
        # Mentor sent the invite to institute - fetch mentor user and profile
        sender_user = db.query(User).filter(
            User.id == invite.mentor_id,
            User.user_type == UserTypeEnum.mentor
        ).first()

        if not sender_user or not sender_user.country:
            return None

        # Fetch mentor profile separately
        mentor_profile = data

        # Get profile image data - prioritize mentor profile image, then user profile picture
        profile_image = None
        if mentor_profile and mentor_profile.profile_image_url:
            try:
                profile_image = get_profile_image_data(mentor_profile.profile_image_url)
            except Exception:
                pass

        if not profile_image and sender_user.profile_picture:
            try:
                profile_image = get_profile_image_data(sender_user.profile_picture)
            except Exception:
                profile_image = None

        # Prepare mentor-specific details
        mentor_bio = None
        mentor_experience_years = None
        mentor_hourly_rate = None
        mentor_languages = None

        # Populate mentor profile details
        if mentor_profile:
            mentor_bio = mentor_profile.bio
            mentor_experience_years = mentor_profile.experience_years
            mentor_hourly_rate = float(mentor_profile.hourly_rate) if mentor_profile.hourly_rate else None

            # Parse languages from JSON
            if mentor_profile.languages:
                try:
                    mentor_languages = json.loads(mentor_profile.languages) if isinstance(mentor_profile.languages, str) else mentor_profile.languages
                except (json.JSONDecodeError, TypeError):
                    mentor_languages = []

        try:
            return InvitationSenderDetails(
                id=sender_user.id,
                username=sender_user.username or "",
                email=sender_user.email or "",
                profile_picture=sender_user.profile_picture,
                profile_image=profile_image,
                # Mentor-specific fields
                mentor_bio=mentor_bio,
                mentor_experience_years=mentor_experience_years,
                mentor_hourly_rate=mentor_hourly_rate,
                mentor_languages=mentor_languages,
                # Institute fields set to None for mentor sender
                institute_name=None,
                institute_description=None,
                institute_website=None,
                institute_city=None,
                institute_country=None,
                institute_is_verified=None,
                institute_logo=None
            )
        except Exception as e:
            print(f"Error creating InvitationSenderDetails for mentor: {e}")
            return None

    else:
        return None




def get_mentor_with_profile_by_id(db: Session, mentor_id: uuid.UUID) -> MentorDetailedOut:
    """Get mentor by ID with only profile details (no stats)."""

    # Fetch mentor user + profile
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Ensure mentor profile exists
    if not user.mentor_profile:
        mentor_profile = MentorProfile(user_id=user.id)
        db.add(mentor_profile)
        db.commit()
        db.refresh(mentor_profile)
        user.mentor_profile = mentor_profile

    profile = user.mentor_profile

    # Parse JSON fields
    languages = json.loads(profile.languages) if profile.languages else []
    availability_hours = json.loads(profile.availability_hours) if profile.availability_hours else {}

    # Image handling
    primary_image_url = user.profile_picture or profile.profile_image_url
    profile_image_data = get_profile_image_data(primary_image_url, None)

    # Mentor profile schema
    profile_out = MentorProfileOut(
        id=profile.id,
        user_id=profile.user_id,
        bio=profile.bio,
        experience_years=profile.experience_years,
        languages=languages,
        hourly_rate=profile.hourly_rate,
        availability_hours=availability_hours,
        profile_image_url=primary_image_url,
        profile_image=profile_image_data,
        expertise_subjects=[
            {"id": str(s.id), "name": s.name} for s in getattr(profile, "expertise_subjects", []) if s
        ],
        preferred_subjects=[
            {"id": str(s.id), "name": s.name} for s in getattr(profile, "preferred_subjects", []) if s
        ],
        created_at=profile.created_at,
        updated_at=profile.updated_at,
    )

    # Mentor user schema
    user_out = MentorUserOut(
        id=user.id,
        username=user.username,
        email=user.email,
        mobile=user.mobile,
        country=user.country,
        profile_picture=user.profile_picture,
        profile_image=profile_image_data,
        user_type=str(user.user_type.value),
        is_email_verified=user.is_email_verified,
        is_mobile_verified=user.is_mobile_verified,
        created_at=user.created_at,
        mentor_profile=profile_out
    )

    # Final response — ONLY profile data, no stats
    return MentorDetailedOut(
        user=user_out,
        profile=profile_out,
        total_competitions=0,
        active_institutes=0,
        average_rating=None,
        verification_status="pending"
    )



def get_mentors(
    db: Session,
    page: int = 1,
    size: int = 20,
    search: Optional[str] = None,
    subject_filter: Optional[str] = None,
    min_experience: Optional[int] = None,
    max_hourly_rate: Optional[float] = None
) -> MentorListResponse:
    """Get mentors with filtering and pagination"""
    # Calculate skip for pagination
    skip = (page - 1) * size

    query = db.query(User).options(
        joinedload(User.mentor_profile).joinedload(MentorProfile.expertise_subjects),
        joinedload(User.mentor_profile).joinedload(MentorProfile.preferred_subjects)
    ).join(MentorProfile, MentorProfile.user_id == User.id).filter(User.user_type == UserTypeEnum.mentor)

    # Add search functionality
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term),
                MentorProfile.bio.ilike(search_term)
            )
        )

    # Filter by subject expertise
    if subject_filter:
        query = query.join(MentorProfile.expertise_subjects).filter(
            Subject.name.ilike(f"%{subject_filter}%")
        )

    # Filter by minimum experience
    if min_experience is not None:
        query = query.filter(MentorProfile.experience_years >= min_experience)

    # Filter by maximum hourly rate
    if max_hourly_rate is not None:
        query = query.filter(MentorProfile.hourly_rate <= max_hourly_rate)

    # Get total count for pagination
    total = query.count()

    # Get mentors with pagination
    mentors = query.offset(skip).limit(size).all()

    # Convert to MentorListOut objects
    mentor_list = []
    for user in mentors:
        mentor_profile = user.mentor_profile

        # Get profile image data - prioritize user.profile_picture over mentor_profile.profile_image_url
        user_profile_picture = user.profile_picture
        mentor_profile_image_url = getattr(mentor_profile, 'profile_image_url', None)
        primary_image_url = user_profile_picture or mentor_profile_image_url

        # Get profile image data as base64
        profile_image_data = get_profile_image_data(primary_image_url, None) if primary_image_url else None

        # Get expertise areas from subjects
        expertise_areas = []
        if mentor_profile and hasattr(mentor_profile, 'expertise_subjects') and mentor_profile.expertise_subjects:
            expertise_areas = [subject.name for subject in mentor_profile.expertise_subjects if subject]

        # Parse languages from JSON
        languages = []
        if mentor_profile and mentor_profile.languages:
            try:
                languages = json.loads(mentor_profile.languages) if mentor_profile.languages else []
            except (json.JSONDecodeError, TypeError):
                languages = []

        # Create full name from username (could be enhanced with first_name/last_name if available)
        full_name = user.username

        mentor_list.append(MentorListOut(
            id=user.id,
            username=user.username,
            full_name=full_name,
            email=user.email,
            mobile=user.mobile,
            country=user.country,
            bio=mentor_profile.bio if mentor_profile else None,
            expertise_areas=expertise_areas,
            experience_years=mentor_profile.experience_years if mentor_profile else None,
            current_position=None,  # Not available in current schema
            hourly_rate=mentor_profile.hourly_rate if mentor_profile else None,
            languages=languages,
            rating=None,  # Not implemented yet
            is_verified=user.is_email_verified,
            verification_status="pending",  # Default status
            profile_image_url=primary_image_url,
            profile_image=profile_image_data,
            created_at=user.created_at
        ))

    # Calculate pagination info
    has_next = (skip + size) < total
    has_prev = page > 1

    return MentorListResponse(
        mentors=mentor_list,
        total=total,
        page=page,
        size=size,
        has_next=has_next,
        has_prev=has_prev
    )
   
def update_mentor_profile(
    db: Session,
    mentor_id: uuid.UUID,
    profile_update: MentorProfileUpdate
) -> MentorDetailedOut:
    """Update mentor profile"""
    
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    profile = user.mentor_profile
    if not profile:
        raise HTTPException(status_code=404, detail="Mentor profile not found")
    
    # Update profile fields
    update_data = profile_update.model_dump(exclude_unset=True)

    # Handle subject relationships separately
    expertise_subject_ids = update_data.pop('expertise_subject_ids', None)
    preferred_subject_ids = update_data.pop('preferred_subject_ids', None)

    for field, value in update_data.items():
        if hasattr(profile, field):
            # Handle JSON fields
            if field in ['languages'] and value:
                setattr(profile, field, json.dumps(value))
            elif field == 'availability_hours' and value:
                setattr(profile, field, json.dumps(value))
            else:
                setattr(profile, field, value)

    # Update expertise subjects
    if expertise_subject_ids is not None:
        from Models.users import Subject
        profile.expertise_subjects.clear()
        if expertise_subject_ids:
            expertise_subjects = db.query(Subject).filter(Subject.id.in_(expertise_subject_ids)).all()
            profile.expertise_subjects.extend(expertise_subjects)

    # Update preferred subjects
    if preferred_subject_ids is not None:
        from Models.users import Subject
        profile.preferred_subjects.clear()
        if preferred_subject_ids:
            preferred_subjects = db.query(Subject).filter(Subject.id.in_(preferred_subject_ids)).all()
            profile.preferred_subjects.extend(preferred_subjects)

    db.commit()
    db.refresh(user)
    
    return get_mentor_with_profile_by_id(db, mentor_id)


def create_invite_to_institute(
    db: Session,
    mentor_id: uuid.UUID,
    invite_data: MentorInstituteInviteSchema
) -> MentorInstituteInviteOut:
    """Mentor sends invite to institute"""

    # Verify mentor exists
    mentor = db.query(User).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Verify institute exists
    institute = db.query(User).filter(
        User.id == invite_data.receiver_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Check if invite already exists
    existing_invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.mentor_id == mentor_id,
        MentorInstituteInviteModel.institute_id == invite_data.receiver_id,
        MentorInstituteInviteModel.status == "pending"
    ).first()

    if existing_invite:
        raise HTTPException(status_code=400, detail="Pending invite already exists")

    # Create invite
    invite = MentorInstituteInviteModel(
        mentor_id=mentor_id,
        institute_id=invite_data.receiver_id,
        receiver_id=invite_data.receiver_id,  # Who receives the invite (institute in this case)
        mentor_email=mentor.email,  # Populate mentor email
        invitation_message=invite_data.invitation_message,
        proposed_hourly_rate=invite_data.proposed_hourly_rate,
        proposed_hours_per_week=invite_data.proposed_hours_per_week,
        expertise_areas_needed=invite_data.expertise_areas_needed,
        contract_terms=invite_data.contract_terms,
        expires_at=invite_data.expires_at,
        # Legacy fields for backward compatibility
        hourly_rate=invite_data.hourly_rate or invite_data.proposed_hourly_rate,
        hours_per_week=invite_data.hours_per_week or invite_data.proposed_hours_per_week,
        received_by="institute"  # Institute receives this invite
    )
    db.add(invite)
    db.commit()
    db.refresh(invite)

    return MentorInstituteInviteOut.model_validate(invite)


def create_invite_to_mentor(
    db: Session,
    institute_id: uuid.UUID,
    invite_data: MentorInstituteInviteSchema
) -> MentorInstituteInviteOut:
    """Institute sends invite to mentor"""

    # Verify institute exists
    institute = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Verify mentor exists
    mentor = db.query(User).filter(
        User.id == invite_data.receiver_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Check if invite already exists
    existing_invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.mentor_id == invite_data.receiver_id,
        MentorInstituteInviteModel.institute_id == institute_id,
        MentorInstituteInviteModel.status == "pending"
    ).first()

    if existing_invite:
        raise HTTPException(status_code=400, detail="Pending invite already exists")

    # Create invite
    invite = MentorInstituteInviteModel(
        mentor_id=invite_data.receiver_id,
        institute_id=institute_id,
        receiver_id=invite_data.receiver_id,  # Who receives the invite (mentor in this case)
        mentor_email=mentor.email,  # Populate mentor email
        invitation_message=invite_data.invitation_message,
        proposed_hourly_rate=invite_data.proposed_hourly_rate,
        proposed_hours_per_week=invite_data.proposed_hours_per_week,
        expertise_areas_needed=invite_data.expertise_areas_needed,
        contract_terms=invite_data.contract_terms,
        expires_at=invite_data.expires_at,
        # Legacy fields for backward compatibility
        hourly_rate=invite_data.hourly_rate or invite_data.proposed_hourly_rate,
        hours_per_week=invite_data.hours_per_week or invite_data.proposed_hours_per_week,
        received_by="mentor"  # Mentor receives this invite
    )
    db.add(invite)
    db.commit()
    db.refresh(invite)

    return MentorInstituteInviteOut.model_validate(invite)

def list_sent_invitations(
    db: Session,
    sender_id: uuid.UUID,
    sender_type: str,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> InvitationListResponse:
    """List invitations sent by a user (mentor or institute)"""

    skip = (page - 1) * size

    # Build query based on sender type
    if sender_type == "mentor":
        query = db.query(MentorInstituteInviteModel).filter(
            MentorInstituteInviteModel.mentor_id == sender_id,
            MentorInstituteInviteModel.received_by == "institute"
        )
        data = db.query(MentorProfile).filter(
            MentorProfile.user_id == sender_id
        ).first()

    elif sender_type == "institute":
        query = db.query(MentorInstituteInviteModel).filter(
            MentorInstituteInviteModel.institute_id == sender_id,
            MentorInstituteInviteModel.received_by == "mentor"
        )
        data = db.query(InstituteProfile).filter(
            InstituteProfile.user_id == sender_id
        ).first()
    else:
        raise HTTPException(status_code=400, detail="Invalid sender type")

    if status_filter:
        query = query.filter(MentorInstituteInviteModel.status == status_filter)

    total = query.count()
    invites = query.order_by(desc(MentorInstituteInviteModel.invited_at)).offset(skip).limit(size).all()
    # Convert to response format with sender details
    invite_details = []
    for invite in invites:
        invite_out = MentorInstituteInviteOut.model_validate(invite)
        # Populate sender details
        sender_details = _populate_sender_details(db, invite, data)
        invite_out.sender = sender_details
        invite_details.append(invite_out)

    return InvitationListResponse(
        invitations=invite_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )




def list_received_invitations(
    db: Session,
    receiver_id: uuid.UUID,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> InvitationListResponse:
    """List invitations received by a user (mentor or institute)"""

    skip = (page - 1) * size

    query = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.receiver_id == receiver_id
    )
    data = db.query(MentorProfile).filter(
        MentorProfile.user_id == receiver_id
    ).first()

    if status_filter:
        query = query.filter(MentorInstituteInviteModel.status == status_filter)

    total = query.count()
    invites = query.order_by(desc(MentorInstituteInviteModel.invited_at)).offset(skip).limit(size).all()

    # Convert to response format with sender details
    invite_details = []
    for invite in invites:
        invite_out = MentorInstituteInviteOut.model_validate(invite)
        # Populate sender details
        sender_details = _populate_sender_details(db, invite, data)
        invite_out.sender = sender_details
        invite_details.append(invite_out)

    return InvitationListResponse(
        invitations=invite_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )



def respond_to_received_invite(
    db: Session,
    user_id: uuid.UUID,
    invite_id: uuid.UUID,
    response: dict
) -> CollaborationDetails:
    """User (mentor or institute) responds to an invite they received"""

    # Get the invite - check both mentor and institute scenarios
    invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.id == invite_id,
        or_(
            and_(
                MentorInstituteInviteModel.receiver_id == user_id,
                MentorInstituteInviteModel.received_by == "mentor"
            ),
            and_(
                MentorInstituteInviteModel.receiver_id == user_id,
                MentorInstituteInviteModel.received_by == "institute"
            )
        )
    ).first()

    if not invite:
        raise HTTPException(status_code=404, detail="Invite not found")

    if invite.status != "pending":
        raise HTTPException(status_code=400, detail="Invite already responded to")

    # Update invite status based on response
    action = response.get("action", "").lower()

    if action == "accept":
        invite.status = "accepted"
        invite.responded_at = datetime.now(timezone.utc)

        # Create collaboration
        collaboration = MentorInstituteAssociation(
            mentor_id=invite.mentor_id,
            institute_id=invite.institute_id,
            status="active",
            hourly_rate=response.get("hourly_rate") or invite.proposed_hourly_rate or invite.hourly_rate,
            hours_per_week=response.get("hours_per_week") or invite.proposed_hours_per_week or invite.hours_per_week,
            start_date=datetime.now(timezone.utc),
            created_from_invite_id=invite.id
        )

        db.add(collaboration)
        db.commit()
        db.refresh(collaboration)

        # Import the helper function from our new CRUD
        from Cruds.Mentors.CollaborationCrud import _format_collaboration_details
        return _format_collaboration_details(db, collaboration)

    elif action == "reject":
        invite.status = "declined"
        invite.responded_at = datetime.now(timezone.utc)
        invite.response_message = response.get("message", "Invitation declined")
        db.commit()
        return None  # No collaboration created for rejection

    else:
        raise HTTPException(status_code=400, detail="Invalid action. Use 'accept' or 'reject'")





