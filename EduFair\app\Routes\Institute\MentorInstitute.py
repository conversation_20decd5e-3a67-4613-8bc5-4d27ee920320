"""
Routes for Mentor-Institute Invitations Management
Handles ALL invitation operations (sending, receiving, accepting/rejecting)
For collaboration management, use the Collaborations router
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from uuid import UUID
from typing import Optional

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

# Import CRUD functions
from Cruds.Institute.Mentor import (
    create_invite_to_mentor, create_invite_to_institute,
    list_sent_invitations, list_received_invitations,
    respond_to_received_invite
)
from Schemas.Mentors.MentorInstitutes import (
    MentorInstituteInvite, MentorInstituteInviteOut, InvitationListResponse,
    CollaborationDetails
)

router = APIRouter()

# =========================================================
#                   SENDING INVITATIONS
# =========================================================

@router.post("/send-to-mentor", response_model=MentorInstituteInviteOut)
def send_invitation_to_mentor(
    invitation_data: MentorInstituteInvite,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Institute sends invitation to mentor"""
    current_user = get_current_user(token, db)
    return create_invite_to_mentor(db, current_user.id, invitation_data)


@router.post("/send-to-institute", response_model=MentorInstituteInviteOut)
def send_invitation_to_institute(
    invitation_data: MentorInstituteInvite,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Mentor sends invitation to institute"""
    current_user = get_current_user(token, db)
    return create_invite_to_institute(db, current_user.id, invitation_data)


# =========================================================
#                   LISTING INVITATIONS
# =========================================================

@router.get("/sent", response_model=InvitationListResponse)
def get_sent_invitations(
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Get invitations sent by current user"""
    current_user = get_current_user(token, db)

    return list_sent_invitations(
        db=db,
        sender_id=current_user.id,
        sender_type=current_user.user_type.value,
        page=page,
        size=size,
        status_filter=status_filter
    )


@router.get("/received", response_model=InvitationListResponse)
def get_received_invitations(
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Get invitations received by current user"""
    current_user = get_current_user(token, db)
    return list_received_invitations(
        db=db,
        receiver_id=current_user.id,
        page=page,
        size=size,
        status_filter=status_filter
    )


# =========================================================
#                   RESPONDING TO INVITATIONS
# =========================================================

@router.post("/{invitation_id}/accept", response_model=CollaborationDetails)
def accept_invitation(
    invitation_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Accept an invitation and automatically create collaboration"""
    current_user = get_current_user(token, db)
    response_data = {"action": "accept"}
    collaboration = respond_to_received_invite(db, current_user.id, invitation_id, response_data)
    if not collaboration:
        raise HTTPException(status_code=500, detail="Failed to create collaboration")
    return collaboration


@router.post("/{invitation_id}/reject")
def reject_invitation(
    invitation_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Reject an invitation"""
    current_user = get_current_user(token, db)
    response_data = {"action": "reject"}
    respond_to_received_invite(db, current_user.id, invitation_id, response_data)
    return {"message": "Invitation rejected successfully"}
